"""
Client Configuration Module for SOC Jira Tracker

This module manages client organizations and their configurations for Jira automation.
It provides centralized management of client lists and validation functions.
"""

from typing import List, Dict, Set
from enum import Enum


class ClientType(Enum):
    """Enum for different types of clients"""
    STANDARD = "standard"
    FIREWALL = "firewall"
    SIEM = "siem"
    GUARDIUM = "guardium"


class ClientConfig:
    """
    Centralized configuration for client organizations
    """
    
    # All available clients in the system
    ALL_CLIENTS = {
        "MAG": ClientType.STANDARD,
        "PDRM": ClientType.STANDARD,
        "Astro": ClientType.STANDARD,
        "Firefly": ClientType.STANDARD,
        "EPF": ClientType.STANDARD,
        "EPF Guardium": ClientType.GUARDIUM,
        "UMWT": ClientType.STANDARD,
        "UTP": ClientType.STANDARD,  # Existing client to maintain compatibility
        "Jupem": ClientType.STANDARD,
        "MIDF": ClientType.STANDARD,
        "TNGDigital": ClientType.STANDARD,
        "NEM": ClientType.STANDARD,
        "NXT": ClientType.STANDARD,
        "PDRM Firewall": ClientType.FIREWALL,
        "PLUS": ClientType.STANDARD,
        "NADI Tech": ClientType.STANDARD,
        "EPF SIEM": ClientType.SIEM
    }
    
    # Standard MSOC clients (for general SOC export)
    MSOC_CLIENTS = [
        "MAG", "PDRM", "Astro", "Firefly", "EPF", "EPF Guardium", 
        "UMWT", "UTP", "Jupem", "MIDF", "TNGDigital", "NEM", "NXT", 
        "PLUS", "NADI Tech", "EPF SIEM"
    ]
    
    # Firewall-specific clients
    FIREWALL_CLIENTS = ["PDRM Firewall"]
    
    @classmethod
    def get_all_clients(cls) -> List[str]:
        """Get all available client names"""
        return list(cls.ALL_CLIENTS.keys())
    
    @classmethod
    def get_msoc_clients(cls) -> List[str]:
        """Get clients for MSOC export"""
        return cls.MSOC_CLIENTS.copy()
    
    @classmethod
    def get_firewall_clients(cls) -> List[str]:
        """Get firewall-specific clients"""
        return cls.FIREWALL_CLIENTS.copy()
    
    @classmethod
    def get_clients_by_type(cls, client_type: ClientType) -> List[str]:
        """Get clients filtered by type"""
        return [client for client, ctype in cls.ALL_CLIENTS.items() if ctype == client_type]
    
    @classmethod
    def is_valid_client(cls, client_name: str) -> bool:
        """Check if a client name is valid"""
        return client_name in cls.ALL_CLIENTS
    
    @classmethod
    def validate_client_list(cls, client_list: List[str]) -> Dict[str, List[str]]:
        """
        Validate a list of client names
        Returns dict with 'valid' and 'invalid' keys
        """
        valid_clients = []
        invalid_clients = []
        
        for client in client_list:
            if cls.is_valid_client(client):
                valid_clients.append(client)
            else:
                invalid_clients.append(client)
        
        return {
            'valid': valid_clients,
            'invalid': invalid_clients
        }
    
    @classmethod
    def get_jql_client_list(cls, client_list: List[str] = None) -> str:
        """
        Generate JQL-formatted client list for queries
        If no client_list provided, uses MSOC_CLIENTS
        """
        if client_list is None:
            client_list = cls.MSOC_CLIENTS
        
        # Validate clients
        validation = cls.validate_client_list(client_list)
        if validation['invalid']:
            raise ValueError(f"Invalid clients found: {validation['invalid']}")
        
        # Format for JQL (quote clients with spaces)
        formatted_clients = []
        for client in client_list:
            if ' ' in client:
                formatted_clients.append(f'"{client}"')
            else:
                formatted_clients.append(client)
        
        return ', '.join(formatted_clients)


class PDRMFirewallDataStructure:
    """
    Data structure definition for PDRM Firewall tickets
    Defines the exact field order and types as requested
    """
    
    # Required fields in exact order
    REQUIRED_FIELDS = [
        "issue_key",
        "summary", 
        "pdrm_fault_category",
        "closing_time",
        "pdrm_acknowledge_venue",
        "tm_pdrm_holding_time",
        "total_time_taken",
        "pdrm_acknowledge_time",
        "pdrm_tl_holding_time",
        "states",
        "comment",
        "analyst_name"
    ]
    
    # Field mappings from Jira custom fields to our structure
    JIRA_FIELD_MAPPING = {
        "issue_key": "Issue key",
        "summary": "Summary",
        "pdrm_fault_category": "Custom field (PDRM Fault Category)",
        "closing_time": "Custom field (Closing Time)",
        "pdrm_acknowledge_venue": "Custom field (PDRM Acknowledge Venue)",
        "tm_pdrm_holding_time": "Custom field (TM/PDRM Holding Time)",
        "total_time_taken": "Custom field (Total Time Taken)",
        "pdrm_acknowledge_time": "Custom field (PDRM Acknowledge Time)",
        "pdrm_tl_holding_time": "Custom field (PDRM TL Holding Time)",
        "states": "Status",
        "comment": "Comment",
        "analyst_name": "Custom field (Analyst Name)"
    }
    
    # Derived fields that need formula placeholders
    DERIVED_FIELDS = {
        "tm_pdrm_holding_time": "=closing_time - pdrm_acknowledge_time",
        "total_time_taken": "=closing_time - issue_created_time",
        "pdrm_tl_holding_time": "=pdrm_acknowledge_time - tl_start_time"
    }
    
    @classmethod
    def get_field_order(cls) -> List[str]:
        """Get the required field order"""
        return cls.REQUIRED_FIELDS.copy()
    
    @classmethod
    def get_jira_mapping(cls) -> Dict[str, str]:
        """Get the mapping from our fields to Jira field names"""
        return cls.JIRA_FIELD_MAPPING.copy()
    
    @classmethod
    def get_derived_fields(cls) -> Dict[str, str]:
        """Get derived fields and their formula placeholders"""
        return cls.DERIVED_FIELDS.copy()
    
    @classmethod
    def validate_data_structure(cls, data_dict: Dict) -> Dict[str, List[str]]:
        """
        Validate that a data dictionary contains all required fields
        Returns dict with 'missing' and 'extra' keys
        """
        provided_fields = set(data_dict.keys())
        required_fields = set(cls.REQUIRED_FIELDS)
        
        missing_fields = list(required_fields - provided_fields)
        extra_fields = list(provided_fields - required_fields)
        
        return {
            'missing': missing_fields,
            'extra': extra_fields
        }


def print_client_summary():
    """Print a summary of all configured clients"""
    print("=== SOC Jira Tracker - Client Configuration Summary ===\n")
    
    print("All Available Clients:")
    for client, client_type in ClientConfig.ALL_CLIENTS.items():
        print(f"  - {client} ({client_type.value})")
    
    print(f"\nMSOC Clients ({len(ClientConfig.MSOC_CLIENTS)}):")
    for client in ClientConfig.MSOC_CLIENTS:
        print(f"  - {client}")
    
    print(f"\nFirewall Clients ({len(ClientConfig.FIREWALL_CLIENTS)}):")
    for client in ClientConfig.FIREWALL_CLIENTS:
        print(f"  - {client}")
    
    print(f"\nPDRM Firewall Data Fields ({len(PDRMFirewallDataStructure.REQUIRED_FIELDS)}):")
    for i, field in enumerate(PDRMFirewallDataStructure.REQUIRED_FIELDS, 1):
        derived_indicator = " (derived)" if field in PDRMFirewallDataStructure.DERIVED_FIELDS else ""
        print(f"  {i:2d}. {field}{derived_indicator}")


if __name__ == "__main__":
    print_client_summary()
