Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project lead id,Project description,Project url,Priority,Resolution,Assignee,Assignee Id,Reporter,Reporter Id,Creator,Creator Id,Created,Updated,Last Viewed,Resolved,Due date,Votes,Description,Watchers,Watchers Id,Original estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Custom field (Actual end),Custom field (Actual start),Custom field (Affected hardware),Custom field (Affected services),Custom field (Alert Generation Date/Time),Custom field (Analyst Name),Custom field (Approvals),Custom field (Backout plan),Custom field (Category),Custom field (Change risk),Custom field (Change type),Custom field (Client Ticket Number),Custom field (Closing Time),Custom field (Customer Organization),Custom field (Design),Custom field (Destination IP),Custom field (Detection/Impact),Custom field (Development),Custom field (Duration),Custom field (EPF Collector IP),Custom field (Epic Color),Custom field (Epic Name),Custom field (Epic Status),Custom field (Hostname),Custom field (Impact),Custom field (Incident Source/Tool),Custom field (Investigation reason),Custom field (Issue color),Custom field (Last Follow Up),Custom field (Locked forms),Custom field (Major incident),Custom field (Month),Custom field (Notification Sent out Date/Time),Custom field (Notify Date),Custom field (Open forms),Custom field (Operational categorization),Custom field (PDRM Acknowledge Time),Custom field (PDRM Acknowledge Venue),Custom field (PDRM Fault Category),Custom field (PDRM TL Holding Time),Custom field (Pending reason),Custom field (Planned end),Custom field (Planned start),Custom field (Product categorization),Custom field (Project overview key),Custom field (Project overview status),Custom field (Rank),Custom field (Request Type),Custom field (Request language),Custom field (Request participants),Custom field (Resolutions Notes),Custom field (Responders),Custom field (Root cause),Custom field (SLA Status),Satisfaction rating,Custom field (Satisfaction date),Custom field (Security Incident Category),Custom field (Severity),Custom field (Source),Custom field (Source IP/Hostname),Custom field (Start date),Custom field (Story point estimate),Custom field (Submitted forms),Custom field (TM/PDRM Holding Time),Custom field (Target end),Custom field (Target start),Custom field (Team),Custom field (Test),Custom field (Test plan),Custom field (Time to close after resolution),Custom field (Time to first response),Custom field (Time to internal escalation),Custom field (Time to resolution),Custom field (Time to resolution),Custom field (Time to review normal change),Custom field (Tool Incident ID),Custom field (Total Time Taken),Custom field (Total forms),Custom field (Urgency),Custom field (Username/Hostname/Log Source),Custom field (Vulnerability),Custom field (Work category),Custom field (Workaround),Custom field ([CHART] Date of First Response),Custom field ([CHART] Time in Status),Comment,Status Category,Status Category Changed
TechLab : IPK Kedah | PS1012624018 | All system down,MSOC-9485,33836,[SOC] Cyber Security Incident,Open,MSOC,MSS-SOC,service_desk,sunil,712020:be01373d-d87a-4a1d-97a7-ab1bff769c05,,,High,,,,Ahmad Fadzli,63ff028d7c30bbd6b33e3440,Ahmad Fadzli,63ff028d7c30bbd6b33e3440,08/May/24 8:09 PM,08/May/24 8:09 PM,10/May/24 5:38 PM,,,0,"Dear TechLab

 

Kindly assist to check, customer inform all system down when line connect to Sophos. After bypass Sophos, able to access system BA

PIC : TSM Kedah En Ikmal +60 12-443 4361

 

 

INC                : INC0004741131

Site Name    : IPK Kedah

Service ID     : PS1012624018",Ahmad Fadzli,63ff028d7c30bbd6b33e3440,,,,,,,,,,,,,08/May/24 4:33 PM,Sharven Paramasivam,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@54b0fa75,,,,,INC0004741131,08/May/24 5:30 PM,PDRM Firewall,,,,,,,,,,,,Email,,,,,,May 2024,08/May/24 5:30 PM,08/May/24 12:00 AM,,,,Email,Techlab,,,,,,,,0|i0430n:,Report a Cyber Security Incident,English,,"We have contacted PIC but unreachable during this time but as per checking, no abnormalities found in the firewall portion. Firewall able to access and monitored UP. We will continue to monitor accordingly.",,,,,,,,,**********,,,,,,,,,,,-9:38,-44:59,-1:38,,,,,,,,,incident-management,,,,,To Do,08/May/24 8:09 PM
MSOC-9286| PDRM | IPD Bau | Firewall Cannot Access,MSOC-9286,33637,[SOC] Cyber Security Incident,Resolved,MSOC,MSS-SOC,service_desk,sunil,712020:be01373d-d87a-4a1d-97a7-ab1bff769c05,,,High,Done,,,Ahmad Fadzli,63ff028d7c30bbd6b33e3440,Ahmad Fadzli,63ff028d7c30bbd6b33e3440,05/May/24 5:33 AM,05/May/24 7:13 PM,10/May/24 5:38 PM,05/May/24 7:13 PM,,0,"Dear Team,

We monitor that the firewall IPD Bau for cannot be accessed.



Action:

Ping Router : Failed  (TTL expired in transit)

Ping Firewall : Failed (TTL expired in transit)





Kindly assist to check on the router.



!blob:https://mail.google.com/fc5a1209-3a9d-4976-8ebf-a0ba326a4029|alt=""image.png""!",Ahmad Fadzli,63ff028d7c30bbd6b33e3440,,,,,,,,,,,,,05/May/24 5:32 AM,Muhammad Fadil Aiman Bin Shamsul,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4aa7d161,,,,,,05/May/24 9:52 AM,PDRM Firewall,,,,,,,,,,,,Email,,,,,,May 2024,05/May/24 5:32 AM,04/May/24 12:00 AM,,,,,PDRM,,,,,,,,0|i041sf:,Report a Cyber Security Incident,English,,,,,,,,,,,***********,,,,,,,,,,,8:00,-13:09,16:00,,,,,,,,,incident-management,,,1_*:*_1_*:*_49193391_*|*_5_*:*_1_*:*_0,05/May/24 7:13 PM;63ff028d7c30bbd6b33e3440;Finding: Firewall power issue. No action taken.,Done,05/May/24 7:13 PM
MSOC-9278 | PDRM | IPD Padang Terap | Firewall Cannot Access,MSOC-9278,33629,[SOC] Cyber Security Incident,Resolved,MSOC,MSS-SOC,service_desk,sunil,712020:be01373d-d87a-4a1d-97a7-ab1bff769c05,,,High,Done,,,Ahmad Fadzli,63ff028d7c30bbd6b33e3440,Ahmad Fadzli,63ff028d7c30bbd6b33e3440,04/May/24 7:36 PM,05/May/24 9:45 AM,10/May/24 5:38 PM,05/May/24 9:45 AM,,0,"Dear Team,

We monitor that the firewall IPD Padang Terap for cannot be accessed.



 

Action:

Ping Router : Failed  (TTL expired in transit)

Ping Firewall : Failed (TTL expired in transit)





Kindly assist to check on the router.",Ahmad Fadzli,63ff028d7c30bbd6b33e3440,,,,,,,,,,,,,04/May/24 6:04 PM,Sim Wickam,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@4d97ac0f,,,,,,04/May/24 10:32 PM,PDRM Firewall,,,,,,,,,,,,,,,,,,May 2024,04/May/24 7:37 PM,04/May/24 12:00 AM,,,,TM acknowledge email (On Whatsapp and Email),PDRM,,,,,,,,0|i041qn:,Report a Cyber Security Incident,English,,,,,,,,,,,,,,,,,,,,,,8:00,-13:39,16:00,,,,,,,,,incident-management,,,1_*:*_1_*:*_50957363_*|*_5_*:*_1_*:*_0,05/May/24 9:45 AM;63ff028d7c30bbd6b33e3440;Finding: Firewall power issue. No action taken.,Done,05/May/24 9:45 AM
TECHLAB | IPD SEREMBAN2(ZH7) | INC0004726721,MSOC-9215,33566,[SOC] Cyber Security Incident,Resolved,MSOC,MSS-SOC,service_desk,sunil,712020:be01373d-d87a-4a1d-97a7-ab1bff769c05,,,High,Done,,,Ahmad Fadzli,63ff028d7c30bbd6b33e3440,Ahmad Fadzli,63ff028d7c30bbd6b33e3440,03/May/24 1:21 PM,04/May/24 2:31 PM,10/May/24 5:38 PM,04/May/24 2:31 PM,,0,"Dear Team,

 

As per checking with PIC En Safuan, no abnormalities found in the firewall portion. Firewall able to access after the user performs some checking on the internal switch. Users suspect internal switch problems, they will check on their portion. 

 

Ping Firewall: OK



!https://mail.google.com/mail/u/0?ui=2&ik=3411857631&attid=0.8&permmsgid=msg-f:1798007248699671297&th=18f3ce1102524301&view=fimg&fur=ip&sz=s0-l75-ft&attbid=ANGjdJ9HJiMOwcGOBqQUVNgcezJsOZ8sgag_SO5-xms9E9rKSz7MCnloh-ypTKTmT1oL6rQWt676xf3i_zUznNs5xCkBGRKZ9cNjG6FVJVKEu5xR39S3WtulcWlaV8M&disp=emb|width=653,height=210,alt=""image.png""!

IP Scanner:

 

!https://mail.google.com/mail/u/0?ui=2&ik=3411857631&attid=0.2&permmsgid=msg-f:1798007248699671297&th=18f3ce1102524301&view=fimg&fur=ip&sz=s0-l75-ft&attbid=ANGjdJ9pOUopC1mwMrwFwupXzrqUgjILky8E47tAYqXlLuAQbrG-ypEuS8B4mLfJVD2wmNDpuAsS1OIYAIRh6BuDKzozy1pC9FaNP1tS1WxaT9cwQRgZScIIqyfTyac&disp=emb|width=450,height=303,alt=""image.png""!

Ports:

!https://mail.google.com/mail/u/0?ui=2&ik=3411857631&attid=0.5&permmsgid=msg-f:1798007248699671297&th=18f3ce1102524301&view=fimg&fur=ip&sz=s0-l75-ft&attbid=ANGjdJ9b_us6ltJH5_2Z-Buy7g9O6bdKY6y0iDa1_Dbin654J3-5fbYba0LBtQY7bF5CWOzUyCX464Sm5hN9e_yd_9BAPGbzH31AX6w4ZOacQOuGysGL79rwoEmqhwU&disp=emb|width=187,height=366,alt=""image.png""!",Ahmad Fadzli,63ff028d7c30bbd6b33e3440,,,,,,,,,,,,,03/May/24 11:56 AM,Chew Jia Zheng,com.atlassian.servicedesk.plugins.approvals.internal.customfield.ApprovalsCFValue@5d0bdfde,,,,,,03/May/24 12:50 PM,PDRM Firewall,,,,,,,,,,,,Email,,,,,,May 2024,03/May/24 12:51 PM,03/May/24 12:00 AM,,,,,,,,,,,,,0|i041cn:,Report a Cyber Security Incident,English,,,,,,,,,,,,,,,,,,,,,,3:21,-24:40,11:21,,,,,,,,,incident-management,,,1_*:*_1_*:*_90613679_*|*_5_*:*_1_*:*_0,"04/May/24 2:31 PM;63ff028d7c30bbd6b33e3440;Finding: As per checking with PIC En Safuan, no abnormalities found in the firewall portion. Firewall able to access after the user performs some checking on the internal switch. Users suspect internal switch problems, they will check on their portion.",Done,04/May/24 2:31 PM
