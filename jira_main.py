import os
import requests
import urllib3
import json
import csv

from datetime import datetime, date
from atlassian import Jira
from requests.auth import HTT<PERSON><PERSON>asic<PERSON>uth
from dotenv import load_dotenv
from jiraone import LOGIN, issue_export

from gdrive_method import *

import pandas as pd

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

load_dotenv('app.env')
jira_ad_username = os.environ.get("JIRA_USERNAME1")
jira_username = os.environ.get("JIRA_USERNAME2")
jira_password = os.environ.get("JIRA_PASSWORD")
jira_server = os.environ.get("JIRA_SERVER")
jira_token = os.environ.get("JIRA_TOKEN")
jira_filter_token = os.environ.get("JIRA_FILTER_TOKEN")
token_user = os.environ.get("TOKEN_USERNAME")

jira_header = {
        "Accept": "application/json",
        "Content-Type": "application/json"
}

def jira_api_auth():
    jira = Jira(jira_server, jira_ad_username, jira_token, cloud=True)
    return jira

def list_jira_projects():
    jira = jira_api_auth()
    projects = jira.projects()

    for project in projects:
        json_project = json.dumps(project)
        key = project['key']
        print(key)

def api_version():
    jira = jira_api_auth()
    print(jira.api_version)

def obtain_fav_filter():
    fav_filters_url = f"{jira_server}/rest/api/3/filter/favourite"
    filters_auth = HTTPBasicAuth(jira_username, jira_filter_token)

    filter_response = requests.request(
        "GET",
        fav_filters_url,
        headers=jira_header,
        auth=filters_auth
    )

    fav_filter_list = []
    if filter_response.status_code == 200:
        favourites = filter_response.json()

        for fav in favourites:
            # print(fav.keys())
            fav_filter_name = fav['name']
            jql_query = fav['jql']
            fav_search_url = fav['searchUrl']
            fav_view_url = fav['viewUrl']
            fav_filter_list.append((fav_filter_name,jql_query,fav_search_url,fav_view_url))

        print(fav_filter_list[1])
        print(f"type : {type(fav_filter_list[1])}")
    else:
        print("Error during request") # probably should include non generic error catching


def return_server_info():
    server_info_url = f"{jira_server}/rest/api/3/serverInfo"


def msoc_export_to_csv():
    auth_file = "jira_one_config.json"
    jira_one_config = json.load(open(auth_file))
    LOGIN(**jira_one_config)

    jql_query = jql_query = 'project = "MSOC" and "customer organization[dropdown]" in (MAG, PDRM, Astro, Firefly, EPF, UTP, Jupem, UMWT, MIDF) and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth())ORDER BY cf[10086] DESC, created DESC'
    current_datetime = datetime.now()
    export_file = current_datetime.strftime("%d%m%y") + "_msoc_"+".csv"

    issue_export(jql=jql_query,final_file=export_file)

def pdrm_fw_export_to_csv():
    auth_file = "jira_one_config.json"
    jira_one_config = json.load(open(auth_file))
    LOGIN(**jira_one_config)

    jql_query = 'project = "MSOC" and "customer organization[dropdown]" = "PDRM Firewall" and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth()) ORDER BY created DESC'
    current_datetime = datetime.now()
    export_file = current_datetime.strftime("%d%m%y") + "_pdrm_"+".csv"

    issue_export(jql=jql_query,final_file=export_file)


def find_csv_files(dir: str):
    csv_files = []
    if os.path.exists(dir):
        for file in os.listdir(dir):
            if file.endswith(".csv"):
                csv_files.append(file)

    else:
        print("The directory provide does not exist")

    if csv_files:
        return csv_files
    else:
        return False


def remove_single_column_if_exists(csv_file_path: str, column_name: str):
    temp_file_path = csv_file_path + '.tmp'
    found_column = False

    with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile, \
        open(temp_file_path, 'w', newline='', encoding='utf-8') as temp_csvfile:
            reader = csv.reader(csvfile)
    writer = csv.writer(temp_csvfile)

    for i, row in enumerate(reader):
        if i == 0:  # Header row
            if column_name in row:
                found_column = True
                index_to_remove = row.index(column_name)
                new_row = [cell for j, cell in enumerate(row) if j != index_to_remove]
                writer.writerow(new_row)
            else:
                writer.writerow(row)
        else:
            new_row = [cell for j, cell in enumerate(row) if j != index_to_remove]
            writer.writerow(new_row)

    if found_column:
        os.remove(csv_file_path)
        os.rename(temp_file_path, csv_file_path)
        print(f"Column '{column_name}' removed from the CSV file.")
    else:
        os.remove(temp_file_path)
        print(f"Column '{column_name}' not found in the CSV file.")


def remove_columns_if_exist(csv_file_path: str, columns_to_remove: list):
    temp_file_path = csv_file_path + '.tmp'
    found_columns = {col.strip(): False for col in columns_to_remove}

    with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile, \
         open(temp_file_path, 'w', newline='', encoding='utf-8') as temp_csvfile:
        reader = csv.reader(csvfile)
        writer = csv.writer(temp_csvfile)

        header = next(reader)
        header = [col.strip() for col in header]  # Strip whitespace from header

        for i, row in enumerate(reader):
            if i == 0:  # Header row
                for col in columns_to_remove:
                    if col.strip() in header:  # Compare stripped version
                        found_columns[col.strip()] = True

                new_header = [header[i] for i in range(len(header)) if header[i] not in columns_to_remove]
                writer.writerow(new_header)
            else:
                new_row = [row[i] for i in range(len(row)) if header[i] not in columns_to_remove]
                writer.writerow(new_row)

    columns_removed = [col for col, found in found_columns.items() if found]
    if any(found_columns.values()):
        os.remove(csv_file_path)
        os.rename(temp_file_path, csv_file_path)
        print(f"Columns '{', '.join(columns_removed)}' removed from the CSV file.")
    else:
        os.remove(temp_file_path)
        print(f"Columns '{', '.join(columns_to_remove)}' not found in the CSV file.")


def get_headers(csv_file_path: str) -> list:
    with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
        reader = csv.reader(csvfile)
        headers = next(reader)
    return headers

def compare_lists(list1: list, list2: list):
    unique_in_list2 = [item for item in list2 if item not in list1]
    return unique_in_list2


def rearrange_columns(input_file: str, output_file: str, desired_column_order: list):
    df = pd.read_csv(input_file)

    missing_columns = [col for col in desired_column_order if col not in df.columns]
    if missing_columns:
        print("The following columns are missing from the input CSV file:", missing_columns)
        return

    df = df[desired_column_order]
    df.to_csv(output_file, index=False)


def transform_csv_to_excel_with_formulas(csv_file: str , excel_file: str):
    df = pd.read_csv(csv_file)

    if 'Custom field (SLA Status)' not in df.columns or 'Custom field (Duration)' not in df.columns:
        print("One or both of the specified columns not found in the DataFrame.")
        return

    # Get the indices of the specified columns
    sla_status_index = df.columns.get_loc('Custom field (SLA Status)')
    duration_index = df.columns.get_loc('Custom field (Duration)')
    notification_index = df.columns.get_loc('Custom field (Notification Sent out Date/Time)')
    alert_index = df.columns.get_loc('Custom field (Alert Generation Date/Time)')
    priority_index = df.columns.get_loc('Priority') # Column J

    # Transform the DataFrame into an Excel writer object
    with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet1')

        workbook = writer.book
        worksheet = writer.sheets['Sheet1']

        last_row = len(df) + 1

        # Formula for Custom field (SLA Status)
        for row in range(2, last_row):
            worksheet.write_formula(f'{chr(65 + sla_status_index)}{row}', f'=IF(LEN({chr(65 + priority_index)}{row}), IF(OR(AND(OR({chr(65 + priority_index)}{row}="High",{chr(65 + priority_index)}{row}="Highest"), {chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}<TIME(1,0,1)), AND({chr(65 + priority_index)}{row}="Medium", {chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}<TIME(2,0,1)), AND(OR({chr(65 + priority_index)}{row}="Low",{chr(65 + priority_index)}{row}="Lowest"), {chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}<TIME(23,0,1))), "Met", "Not Met"), "")')

        # Formula for Custom field (Duration)
        for row in range(2, last_row):
            # worksheet.write_formula(f'{chr(65 + duration_index)}{row}',f'=IF({chr(65 + alert_index)}{row}="", "", TEXT({chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row},"hh:mm:ss"))')
            worksheet.write_formula(f'{chr(65 + duration_index)}{row}', f'=IF({chr(65 + alert_index)}{row}="", "", IF({chr(65 + notification_index)}{row}>{chr(65 + alert_index)}{row}, TEXT({chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row},"hh:mm:ss"), TEXT({chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row}+1,"hh:mm:ss")))')

        # Autofit column widths
        for i, col in enumerate(df.columns):
            column_len = max(df[col].astype(str).map(len).max(), len(col))
            worksheet.set_column(i, i, column_len)

    print("Excel file with formulas generated successfully.")


def transform__pdrm_csv_to_excel_with_formulas(csv_file: str , excel_file: str):
    df = pd.read_csv(csv_file)

    if ('Custom field (TM/PDRM Holding Time)' not in df.columns) or ('Custom field (Total Time Taken)' not in df.columns) or ('Custom field (PDRM TL Holding Time)' not in df.columns):
        print("One of the specified columns not found in the DataFrame.")
        return

    # Get the indices of the specified columns
    holding_time_index = df.columns.get_loc('Custom field (TM/PDRM Holding Time)') # column N,
    ttl_time_index = df.columns.get_loc('Custom field (Total Time Taken)') # Column O, most important
    closing_time_index = df.columns.get_loc('Custom field (Closing Time)') # column J
    start_time_index = df.columns.get_loc('Custom field (Notification Sent out Date/Time)') # Column I
    pdrm_holding_index = df.columns.get_loc('Custom field (PDRM TL Holding Time)') # column Q
    tm_holding_time = df.columns.get_loc('Custom field (PDRM TL Holding Time)')
    tm_acknowledge_time = df.columns.get_loc('Custom field (PDRM Acknowledge Time)')

    # Transform the DataFrame into an Excel writer object
    with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet1')

        workbook = writer.book
        worksheet = writer.sheets['Sheet1']

        last_row = len(df) + 1

        # Formula for Custom field (Total Time Taken)
        for row in range(2, last_row):
            # worksheet.write_formula(f'{chr(65 + duration_index)}{row}',f'=IF({chr(65 + alert_index)}{row}="", "", TEXT({chr(65 + notification_index)}{row}-{chr(65 + alert_index)}{row},"hh:mm:ss"))')
            worksheet.write_formula(f'{chr(65 + ttl_time_index)}{row}',f'=SUM({chr(65 + closing_time_index)}{row}-{chr(65 + start_time_index)}{row}),"[hh]:mm:ss")')

        # TM holding time
        for row in range(2,last_row):
            worksheet.write_formula(f'{chr(65 + pdrm_holding_index)}{row}',f'=IF(OR({chr(65 + tm_acknowledge_time)}{row}="",C1221=""),"",TEXT({chr(65 + ttl_time_index)}{row}-{chr(65 + start_time_index)}{row},"hh:mm:ss"))')

        # Formula for TM/PDRM
        # for row in range(2, last_row):
        #     worksheet.write_formula(f'{chr(65 + holding_time_index)}{row}', f'=IF(OR(ISBLANK({chr(65 + ttl_time_index)}{row}), ISBLANK({chr(65 + tm_holding_time)}{row})), 0, TEXT(({chr(65 + ttl_time_index)}{row}-{chr(65 + tm_holding_time)}{row}), "[hh]:mm:ss"))')
        for row in range(2, last_row):
            worksheet.write_formula(f'{chr(65 + holding_time_index)}{row}', f'=IF({chr(65 + ttl_time_index)}{row}=0, 0, IF(OR(ISBLANK({chr(65 + tm_holding_time)}{row}), {chr(65 + tm_holding_time)}{row}=0), 0, TEXT(({chr(65 + ttl_time_index)}{row}-{chr(65 + tm_holding_time)}{row}), "[hh]:mm:ss")))')


        # Autofit column widths
        for i, col in enumerate(df.columns):
            column_len = max(df[col].astype(str).map(len).max(), len(col))
            worksheet.set_column(i, i, column_len)

    print("Excel file with formulas generated successfully.")


def convert_date_format(input_file, output_file, date_column ='Custom field (Notification Sent out Date/Time)', month_column = 'Custom field (Month)'):
    # Define the date parsing and formatting functions
    def parse_date(date_str):
        return datetime.strptime(date_str, "%d/%b/%y %I:%M %p")
        # return datetime.strptime(date_str, "%d/%m/%Y %H:%M")

    def format_date(date_obj):
        return str(date_obj.strftime("%b %Y"))

    # Read the CSV file, convert dates, and write to the output CSV file
    encodings_to_try = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']

    for encoding in encodings_to_try:
        try:
            print(f"Trying to read CSV with {encoding} encoding...")
            with open(input_file, 'r', encoding=encoding) as infile, open(output_file, 'w', newline='', encoding='utf-8') as outfile:
                reader = csv.DictReader(infile)
                fieldnames = reader.fieldnames

                writer = csv.DictWriter(outfile, fieldnames=fieldnames)
                writer.writeheader()

                for row in reader:
                    if row[month_column]:  # Check if the "Custom field (Month)" column has a value
                        date_str = row[date_column]
                        date_obj = parse_date(date_str)
                        formatted_date = format_date(date_obj)
                        row[month_column] = formatted_date

                    writer.writerow(row)

            print(f"Successfully processed file with {encoding} encoding")
            return  # Exit the function if successful

        except UnicodeDecodeError as e:
            print(f"Failed with {encoding} encoding: {str(e)}")
            continue  # Try the next encoding

    # If we get here, all encodings failed
    print("ERROR: Failed to process the CSV file with all attempted encodings.")
    print("You may need to manually convert the file encoding or check for corrupt characters.")


def delete_current_day_csv_files(directory):
    # Get the current date
    current_date = date.today()

    # Iterate over all files in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.csv'):
            filepath = os.path.join(directory, filename)
            # Get the modification time of the file
            modification_time = os.path.getmtime(filepath)
            modification_date = datetime.fromtimestamp(modification_time).date()
            # Check if the modification date is the same as the current date
            if modification_date == current_date:
                # Delete the file
                os.remove(filepath)
                print(f"Deleted: {filepath}")


def upload_to_drive(file_path:str):
    # Upload the Excel file to Google Drive
    file_to_be_uploaded_paths = [file_path]
    destination_folder_id = '1-OFA499xDXc6ktLv19ShSQ8B58sk1ibR'
    uploaded_files = upload_files_to_drive(file_to_be_uploaded_paths, destination_folder_id)

    # Change to proper logging for monitoring purpose
    for file_info in uploaded_files:
        print('Uploaded:', file_info['title'])
        print('File ID:', file_info['id'])
        print('Link:', file_info['webContentLink'])
        print()


def modify_resolution_column(input_file, output_file,column_name):
    with open(input_file, 'r', newline='', encoding='utf-8') as infile, \
         open(output_file, 'w', newline='', encoding='utf-8') as outfile:
        reader = csv.DictReader(infile)
        fieldnames = reader.fieldnames
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()

        for row in reader:
            resolution = row[column_name]
            # Split the content of the resolution column based on ";"
            parts = resolution.rsplit(';', 1)
            if len(parts) > 1:
                # Get the last element after splitting
                modified_resolution = parts[-1].strip()
                row[column_name] = modified_resolution
            # Write the modified row to the new CSV file
            writer.writerow(row)


################################################# main function here ###########################


def main_msoc_process():
    # generate the msoc csv
    msoc_export_to_csv()

    excel_dir= f"{os.getcwd()}/EXPORT" # directory to store the excel sheet

    desired_column_order = ['Issue key','Custom field (Client Ticket Number)', 'Custom field (Tool Incident ID)', 'Custom field (Customer Organization)', 'Summary', 'Custom field (Alert Generation Date/Time)', 'Custom field (Notification Sent out Date/Time)', 'Custom field (Duration)', 'Custom field (SLA Status)', 'Priority', 'Custom field (Analyst Name)', 'Custom field (Incident Source/Tool)', 'Custom field (Source IP/Hostname)', 'Custom field (Username/Hostname/Log Source)', 'Custom field (Destination IP)', 'Custom field (EPF Collector IP)', 'Custom field (PDRM Fault Category)', 'Custom field (PDRM Acknowledge Venue)', 'Custom field (Detection/Impact)', 'Status', 'Custom field (Month)', 'Custom field (Resolutions Notes)', 'Custom field (Last Follow Up)', 'Custom field (Closing Time)', 'Resolution','Comment']


    current_datetime = datetime.now()
    curr_fmt_datetime =  current_datetime.strftime("%d%m%y")
    curr_msoc_file = curr_fmt_datetime + "_msoc_"

    input_msoc_file = f"{excel_dir}/{curr_msoc_file}.csv" # how to obtain the msoc csv file?
    output_msoc_file = f"{excel_dir}/modified_msoc.csv"

    modify_resolution_column(input_msoc_file, output_msoc_file,column_name='Custom field (Resolutions Notes)')

    rearrange_columns(output_msoc_file, output_msoc_file, desired_column_order)

    date_converted_msoc = f'{excel_dir}/date_conv_msoc.csv'  # Replace with the desired output CSV file
    convert_date_format(input_file=output_msoc_file, output_file=date_converted_msoc)

    modified_msoc_excel = f"{excel_dir}/modified_msoc_{curr_fmt_datetime}.xlsx"
    transform_csv_to_excel_with_formulas(date_converted_msoc, modified_msoc_excel)

    # gdrive upload directory
    upload_to_drive(modified_msoc_excel)



def main_pdrm_process():
    # generate the pdrm fw csv
    pdrm_fw_export_to_csv()

    excel_dir= f"{os.getcwd()}/EXPORT" # directory to store the excel sheet

    desired_column_order = ['Issue key', 'Custom field (Client Ticket Number)', 'Custom field (Notification Sent out Date/Time)', 'Custom field (Last Follow Up)', 'Priority', 'Custom field (Analyst Name)', 'Summary', 'Custom field (PDRM Fault Category)', 'Custom field (Closing Time)', 'Custom field (PDRM Acknowledge Venue)', 'Status', 'Custom field (Resolutions Notes)', 'Custom field (TM/PDRM Holding Time)', 'Custom field (Total Time Taken)', 'Custom field (PDRM Acknowledge Time)', 'Custom field (PDRM TL Holding Time)', 'Custom field (Customer Organization)', 'Custom field (Month)','Comment']

    current_datetime = datetime.now()
    curr_fmt_datetime =  current_datetime.strftime("%d%m%y")
    curr_pdrm_file = curr_fmt_datetime + "_pdrm_"

    input_pdrm_file = f"{excel_dir}/{curr_pdrm_file}.csv" # how to obtain the msoc csv file?
    output_pdrm_file = f"{excel_dir}/modified_pdrm.csv"

    modify_resolution_column(input_pdrm_file, output_pdrm_file,column_name='Comment')

    rearrange_columns(output_pdrm_file, output_pdrm_file, desired_column_order)

    date_converted_pdrm = f'{excel_dir}/date_conv_pdrm.csv'  # Replace with the desired output CSV file
    convert_date_format(input_file=output_pdrm_file, output_file=date_converted_pdrm)

    modified_pdrm_excel = f"{excel_dir}/modified_pdrm_{curr_fmt_datetime}.xlsx"
    transform__pdrm_csv_to_excel_with_formulas(date_converted_pdrm,modified_pdrm_excel)

    # gdrive upload directory
    upload_to_drive(modified_pdrm_excel)

if __name__ == "__main__":
    print("NOTE: make sure the current file in the gdrive is moved to the archive folder")
    pdrm_trigger = True
    msoc_trigger = True
    if pdrm_trigger:
        main_pdrm_process()
    if msoc_trigger:
        main_msoc_process()


    excel_dir= f"{os.getcwd()}/EXPORT" # directory to store the excel sheet
    delete_current_day_csv_files(excel_dir)

    # input_file = r'C:\Work_Folder\SOC\soc_jira_tracker\EXPORT\rename.csv'  # Replace with your input CSV file
    # output_file = r'C:\Work_Folder\SOC\soc_jira_tracker\EXPORT\rename.csv'  # Replace with the desired output CSV file
    # convert_date_format(input_file, output_file)









