PDRM Firewall Data Fields Documentation
==================================================

Required Fields (in exact order):

 1. issue_key
    Jira issue ID (e.g., MSOC-12345)

 2. summary
    Short description of the issue

 3. pdrm_fault_category
    Classification of the fault type

 4. closing_time
    Timestamp when the ticket was closed

 5. pdrm_acknowledge_venue
    Venue acknowledged by PDRM

 6. tm_pdrm_holding_time [DERIVED]
    Derived: closing_time - pdrm_acknowledge_time

 7. total_time_taken [DERIVED]
    Derived: closing_time - issue_created_time

 8. pdrm_acknowledge_time
    Timestamp when PDRM acknowledged

 9. pdrm_tl_holding_time [DERIVED]
    Derived: pdrm_acknowledge_time - tl_start_time

10. states
    Current state or location of the issue

11. comment
    Analyst notes or remarks

12. analyst_name
    Person who handled the case

